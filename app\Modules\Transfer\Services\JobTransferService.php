<?php

namespace App\Modules\Transfer\Services;

use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Client\Constants\DomainStatus;
use App\Util\Constant\UserDomainStatus;
use App\Modules\Client\Services\DomainService;
use App\Modules\DomainClassificationApi\Jobs\DomainClassificationApiUpdateDomainOwnerJob;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Epp\Constants\EppErrorCodes;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\JobRetry\Services\RetryJobService;
use App\Modules\Notification\Services\TransferNotificationService;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Constants\TransferTransactionTypes;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Helper\Domain\DomainParser;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class JobTransferService
{
    use UserLoggerTrait;

    private $dispatchDelayInSeconds = 180; // three minutes

    public static function instance()
    {
        $jobTransferService = new self;

        return $jobTransferService;
    }

    public function userRequestAction(array $params): void
    {
        $this->verifyPendingStatus($params);
        
        app(AuthLogger::class)->info($this->fromWho('Domain transfer user action-'.$params['action'].' start...', $params['email']));
        $response = EppTransferService::instance()->callEppTransferClientResponse($params['domainName'], $params['action'], $params['email']);

        if ($this->isSuccessful($response)) {
            $this->handleSuccessfulUserAction($params, $response['data']['datastore']['response']);
        } elseif ($response['status'] === Config::get('transfer.response.disabled')) {
            $this->handleDisabledFeature($params);
        } else {
            $this->handleFailedUserAction($response, $params['email'], $params['action']);
        }

        // $this->addRetryLogs(QueueConnection::DOMAIN_TRANSFER_RESPONSE, DomainStatus::ACTIVE, $params, $params['domainId']);
        app(AuthLogger::class)->info($this->fromWho('Domain transfer user action-'.$params['action'].' end...', $params['email']));
    }

    public function isSuccessful(array $response): bool
    {
        return $response['status'] === Config::get('transfer.response.ok');
    }

    private function verifyPendingStatus(array $params): void
    {
        $response = EppTransferService::instance()->callTransferQuery($params['domainName'], $params['email']);

        if (! $this->isSuccessful($response)) {
            app(AuthLogger::class)->error($this->fromWho('failed checking the status. Job has been added to the retry logs.', $params['email']));
            throw new Exception(QueueErrorTypes::RETRY);
        }

        if ($response['data']['response']['transferStatus'] === EppDomainStatus::TRANSFER_PENDING) {
            return;
        }
        app(AuthLogger::class)->error($this->fromWho('failed to approve the request. The transfer status is not on pending.', $params['email']));
        throw new Exception(QueueErrorTypes::RETRY);
    }

    private function handleSuccessfulUserAction(array $params, array $response): void
    {
        switch ($params['action']) {
            case TransferTransactionTypes::APPROVE:
                DomainService::instance()->deleteDomain($params['domainId'], DomainStatus::TRANSFERRED, UserDomainStatus::TRANSFERRED, $params['email']);
                DB::table('domains')->where('id', $params['domainId'])->update(['transferredOut' => $response['transferredOut']]);
                TransferDomainService::instance()->update($params['domainName'], $params['registeredDomainId'], ['status' => TransferRequest::OUTBOUND.'.'.EppDomainStatus::TRANSFER_ADMIN_APPROVED], $params['email']);
                // ScheduleDomainExpiryNotice::dispatch($params['userId'])->delay($this->dispatchDelayInSeconds);
                $this->notify($params['domainName'], $params['userId'], 'Your domain transfer request has been approved.');

                // ! PROCESS DOMAIN CLASSIFICATION HERE
                // $this->processDomainClassificationApi(
                //     $params['domainName'],
                //     $params['email']
                // );

                break;
            case TransferTransactionTypes::REJECT:
                // DomainService::instance()->updateDomainStatus($params['domainId'], DomainStatus::ACTIVE);
                TransferDomainService::instance()->update($params['domainName'], $params['registeredDomainId'], ['status' => TransferRequest::OUTBOUND.'.'.EppDomainStatus::TRANSFER_ADMIN_APPROVED, 'deleted_at' => null], $params['email']);
                $this->notify($params['domainName'], $params['userId'], 'Your request to transfer the domain has been rejected.');
                break;
        }
    }

    private function handleDisabledFeature(array $params): void
    {
        TransferDomainService::instance()->update($params['domainName'], $params['registeredDomainId'], ['deleted_at' => null], $params['email']);
        DomainService::instance()->updateDomainStatus($params['domainId'], DomainStatus::ACTIVE);
        $this->notify($params['userId'], $params['email'], 'Your request to transfer the domain has been rejected.');
    }

    private function handleFailedUserAction(array $response, string $email, string $action): void
    {
        app(AuthLogger::class)->error($this->fromWho('failed to '.$action.' the request. Job has been added to the retry logs.', $email));
        throw new Exception(QueueErrorTypes::RETRY);
    }

    private function notify($domainName, $userId, $message)
    {
      DB::client()->table('notifications')->insert([
        'user_id'      => $userId,
        'title'        => 'Domain Transfer Approved',
        'message'      => $message,
        'redirect_url' => '/domain',
        'created_at'   => now(),
        'updated_at'   => now(),
        'importance'   => 'important',
      ]);
    }

}
